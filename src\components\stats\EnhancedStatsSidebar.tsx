"use client"

import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON>les,
  Timer,
  Music,
  CheckSquare,
  Waves,
  ListMusic,
  Headphones,
  ClipboardList,
  CheckCircle,
  Flag,
  ChevronRight,
  User
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { useState } from "react"
import { useRouter } from "next/navigation"

interface EnhancedStatsSidebarProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

// Define new interfaces for our navigation structure
interface NavItem {
  title: string
  icon: React.ComponentType<{ className?: string }>
  value: string
  description: string
  badge?: string
}

interface NavSection {
  id: string
  title: string
  icon: React.ComponentType<{ className?: string }>
  items?: NavItem[]
  value?: string
  description?: string
}

export function EnhancedStatsSidebar({ activeTab, onTabChange }: EnhancedStatsSidebarProps) {
  const [openSections, setOpenSections] = useState<string[]>(["analytics", "playlist", "tasks"])
  const { state } = useSidebar()
  const router = useRouter();

  const toggleSection = (section: string) => {
    setOpenSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  // Main navigation structure
  const navigationSections: NavSection[] = [
    {
      id: "analytics",
      title: "Analytics",
      icon: BarChart3,
      value: "analytics-overview",
      description: "Core metrics, trends & calendar insights"
    },
    {
      id: "profile",
      title: "Profile",
      icon: User,
      value: "profile",
      description: "Account settings & personal information"
    },
    {
      id: "playlist",
      title: "Playlist",
      icon: Music,
      items: [
        {
          title: "My Playlists",
          icon: ListMusic,
          value: "my-playlists",
          description: "Personal collections"
        },
        {
          title: "Musics",
          icon: Headphones,
          value: "musics",
          description: "Concentration tracks"
        },
        {
          title: "Natural Sounds",
          icon: Waves,
          value: "natural-sounds",
          description: "Background audio"
        }
      ]
    },
    {
      id: "tasks",
      title: "Tasks",
      icon: CheckSquare,
      items: [
        {
          title: "Task Overview",
          icon: ClipboardList,
          value: "task-overview",
          description: "All tasks summary"
        },
        {
          title: "Completed",
          icon: CheckCircle,
          value: "completed-tasks",
          description: "Finished tasks"
        },
        {
          title: "Priority Tasks",
          icon: Flag,
          value: "priority-tasks",
          description: "High priority items"
        }
      ]
    }
  ]

  return (
    <Sidebar variant="sidebar" className="border-r border-border/40 bg-background/95 backdrop-blur-sm">
      {/* Enhanced Header - Aligned with Page Info */}
      <SidebarHeader className="border-b border-border/20 px-6 h-16">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center gap-3">
            <div className="flex h-9 w-9 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/70 shadow-sm shadow-primary/20">
              <Timer className="h-5 w-5 text-white" />
            </div>
            {state === "expanded" && (
              <div
                className="transition-all duration-200 ease-in-out cursor-pointer"
                onClick={() => router.push('/')}
              >
                <h2 className="text-base font-semibold tracking-tight">
                  Pomodoro <span className="text-primary font-bold">365</span>
                </h2>
                {/* <p className="text-xs text-muted-foreground opacity-80">Productivity Dashboard</p> */}
              </div>
            )}
          </div>
          <SidebarTrigger className="h-6 w-6 text-muted-foreground hover:text-foreground transition-colors" />
        </div>
      </SidebarHeader>

      {/* Navigation */}
      <SidebarContent className="px-3 pt-4 pb-1 space-y-0">
        {/* Direct menu items - Analytics and Profile */}
        <SidebarGroup className="py-0">
          <SidebarGroupContent>
            <SidebarMenu className="space-y-0 pt-0 pb-1">
              {/* Analytics */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={() => onTabChange("analytics-overview")}
                  isActive={activeTab === "analytics-overview"}
                  className={cn(
                    "w-full justify-start gap-3 px-3 py-2.5 text-sm group relative cursor-pointer",
                    "hover:bg-accent/50 hover:text-accent-foreground",
                    "data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium",
                    "transition-all duration-150 ease-in-out",
                    "focus-visible:ring-1 focus-visible:ring-primary/20",
                    "rounded-lg"
                  )}
                >
                  <div className={cn(
                    "flex h-5 w-5 items-center justify-center rounded-md shrink-0",
                    "transition-colors duration-150",
                    activeTab === "analytics-overview"
                      ? "text-primary"
                      : "text-muted-foreground group-hover:text-accent-foreground"
                  )}>
                    <BarChart3 className="h-3.5 w-3.5" />
                  </div>

                  <div className="flex-1 text-left min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium truncate">Analytics</span>
                    </div>
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>

              {/* Profile */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={() => onTabChange("profile")}
                  isActive={activeTab === "profile"}
                  className={cn(
                    "w-full justify-start gap-3 px-3 py-2.5 text-sm group relative cursor-pointer",
                    "hover:bg-accent/50 hover:text-accent-foreground",
                    "data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium",
                    "transition-all duration-150 ease-in-out",
                    "focus-visible:ring-1 focus-visible:ring-primary/20",
                    "rounded-lg"
                  )}
                >
                  <div className={cn(
                    "flex h-5 w-5 items-center justify-center rounded-md shrink-0",
                    "transition-colors duration-150",
                    activeTab === "profile"
                      ? "text-primary"
                      : "text-muted-foreground group-hover:text-accent-foreground"
                  )}>
                    <User className="h-3.5 w-3.5" />
                  </div>

                  <div className="flex-1 text-left min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium truncate">Profile</span>
                    </div>
                  </div>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Other sections with subtabs */}
        {navigationSections.slice(2).map((section) => (
          <SidebarGroup key={section.id} className="py-0 mt-1">
            <div>
              <SidebarGroupLabel
                className="group/label text-sm font-medium hover:bg-accent/50 rounded-lg px-3 py-2 cursor-pointer"
                onClick={() => toggleSection(section.id)}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-3">
                    <section.icon className="h-4 w-4 text-muted-foreground group-hover/label:text-foreground transition-colors" />
                    <span className="group-hover/label:text-foreground transition-colors font-medium">{section.title}</span>
                  </div>
                  <motion.div
                    animate={{
                      rotate: openSections.includes(section.id) ? 90 : 0
                    }}
                    transition={{
                      duration: 0.2,
                      ease: [0.4, 0.0, 0.2, 1]
                    }}
                  >
                    <ChevronRight className="h-3 w-3 text-muted-foreground/60 group-hover/label:text-muted-foreground transition-colors" />
                  </motion.div>
                </div>
              </SidebarGroupLabel>

              <motion.div
                initial={false}
                animate={{
                  height: openSections.includes(section.id) ? "auto" : 0,
                  opacity: openSections.includes(section.id) ? 1 : 0
                }}
                transition={{
                  duration: 0.3,
                  ease: [0.4, 0.0, 0.2, 1]
                }}
                style={{ overflow: "hidden" }}
              >
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-0 pt-0 pb-1 pr-2">
                    {section.items?.map((item) => (
                      <SidebarMenuItem key={item.value} className="ml-2">
                        <SidebarMenuButton
                          onClick={() => onTabChange(item.value)}
                          isActive={activeTab === item.value}
                          className={cn(
                            "w-full justify-start gap-3 px-3 py-2.5 text-sm group relative cursor-pointer",
                            "hover:bg-accent/50 hover:text-accent-foreground",
                            "data-[active=true]:bg-red-500/10 data-[active=true]:text-red-600 data-[active=true]:font-medium",
                            "transition-all duration-150 ease-in-out",
                            "focus-visible:ring-1 focus-visible:ring-primary/20",
                            "rounded-lg"
                          )}
                        >
                          <div className={cn(
                            "flex h-5 w-5 items-center justify-center rounded-md shrink-0",
                            "transition-colors duration-150",
                            activeTab === item.value
                              ? "text-red-600"
                              : "text-muted-foreground group-hover:text-accent-foreground"
                          )}>
                            <item.icon className="h-3.5 w-3.5" />
                          </div>

                          <div className="flex-1 text-left min-w-0">
                            <div className="flex items-center justify-between">
                              <span className="font-medium truncate">{item.title}</span>
                              {item.badge && (
                                <Badge
                                  variant="secondary"
                                  className={cn(
                                    "h-4 px-1.5 text-[10px] font-medium shrink-0 ml-2 rounded-md",
                                    item.badge === "AI" && "bg-gradient-to-r from-violet-500 to-indigo-500 text-white border-0 shadow-sm"
                                  )}
                                >
                                  {item.badge === "AI" && <Sparkles className="h-2.5 w-2.5 mr-0.5" />}
                                  {item.badge}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </motion.div>
            </div>
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  )
}
